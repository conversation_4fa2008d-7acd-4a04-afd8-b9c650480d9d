export interface EmailCardProps {
  id: number;
  name: string;
  description: string;
  last_modified: Date;
  status: "draft" | "sent" | "scheduled";
}

export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  created_at: Date;
  last_login?: Date;
  status: "active" | "inactive";
}

export interface Role {
  id: number;
  name: string;
  description: string;
  permissions: string[];
  created_at: Date;
}
