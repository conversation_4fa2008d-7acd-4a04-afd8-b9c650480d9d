import { DataTable } from "@/components/data-table"

import data from "../public/data.json"

export interface EmailCardProps {
  name: string;
  description: string;
  last_modified: Date;
  status: "draft" | "sent" | "scheduled";
}

export default function Home() {
  return (
    <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
      <div className="px-4 lg:px-6">
        <h1 className="text-2xl font-bold">Welcome to GrandValira</h1>
        <p className="text-muted-foreground mt-2">
          Aplicación interna para crear plantillas de correos

          <DataTable data={data} />
        </p>
      </div>
    </div>
  );
}
